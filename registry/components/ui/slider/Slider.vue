<template>
  <div class="slider-container">
    <input
      type="range"
      :min="min"
      :max="max"
      :step="step"
      :value="modelValue"
      @input="handleInput"
      :class="cn('slider', props.class)"
    />
    <div class="slider-track">
      <div 
        class="slider-fill" 
        :style="{ width: `${((modelValue - min) / (max - min)) * 100}%` }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@registry/lib/utils'

interface Props {
  modelValue: number
  min?: number
  max?: number
  step?: number
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  min: 0,
  max: 100,
  step: 1,
})

const emits = defineEmits<{
  (e: 'update:modelValue', value: number): void
}>()

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emits('update:modelValue', Number(target.value))
}
</script>

<style scoped>
.slider-container {
  position: relative;
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}

.slider {
  position: absolute;
  width: 100%;
  height: 4px;
  background: transparent;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  z-index: 2;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #5b48f1;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #5b48f1;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-track {
  position: absolute;
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  z-index: 1;
}

.slider-fill {
  height: 100%;
  background: #5b48f1;
  border-radius: 2px;
  transition: width 0.1s ease;
}

.slider:focus {
  outline: none;
}

.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(91, 72, 241, 0.2);
}

.slider:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(91, 72, 241, 0.2);
}
</style>
