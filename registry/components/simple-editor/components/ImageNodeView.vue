<template>
  <div class="image-node-view" ref="nodeViewRef">
    <Popover v-model:open="showPopover">
      <PopoverTrigger as-child>
        <div 
          class="image-container"
          :class="{ 'selected': isSelected }"
          @click="handleImageClick"
        >
          <img
            :src="node.attrs.src"
            :alt="node.attrs.alt"
            :title="node.attrs.title"
            :style="imageStyle"
            :data-align="node.attrs.align"
            @load="handleImageLoad"
            @error="handleImageError"
          />
          
          <!-- 上传进度显示 -->
          <div v-if="node.attrs.uploading" class="upload-overlay">
            <div class="upload-progress">
              <div 
                class="progress-bar" 
                :style="{ width: `${node.attrs.progress}%` }"
              ></div>
            </div>
            <span class="upload-text">上传中... {{ node.attrs.progress }}%</span>
          </div>
          
          <!-- 错误显示 -->
          <div v-if="node.attrs.error" class="error-overlay">
            <span class="error-message">上传失败: {{ node.attrs.error }}</span>
            <button @click="removeImage" class="retry-button">删除</button>
          </div>
        </div>
      </PopoverTrigger>
      
      <PopoverContent 
        side="top" 
        :side-offset="8"
        class="image-settings-popover"
        @interact-outside="handleInteractOutside"
      >
        <ImageSettings 
          :editor="editor" 
          :imageNode="node"
        />
      </PopoverContent>
    </Popover>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Popover, PopoverContent, PopoverTrigger } from '@registry/components/ui/popover'
import ImageSettings from './ImageSettings.vue'

const props = defineProps({
  editor: {
    type: Object,
    required: true
  },
  node: {
    type: Object,
    required: true
  },
  getPos: {
    type: Function,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const nodeViewRef = ref(null)
const showPopover = ref(false)
const isSelected = ref(false)

// 计算图片样式
const imageStyle = computed(() => {
  const { width, align } = props.node.attrs
  let styles = {
    maxWidth: '100%',
    height: 'auto',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    display: 'block',
    transition: 'all 0.2s ease'
  }
  
  if (width) {
    styles.width = width
  }
  
  // 根据对齐方式设置 margin
  switch (align) {
    case 'left':
      styles.margin = '0'
      break
    case 'right':
      styles.margin = '0 0 0 auto'
      break
    case 'center':
    default:
      styles.margin = '0 auto'
      break
  }
  
  return styles
})

// 监听选中状态
watch(() => props.selected, (selected) => {
  isSelected.value = selected
  if (selected) {
    // 延迟显示 popover，确保选中状态已更新
    nextTick(() => {
      showPopover.value = true
    })
  } else {
    showPopover.value = false
  }
})

const handleImageClick = (event) => {
  event.preventDefault()
  event.stopPropagation()
  
  // 选中当前图片节点
  const pos = props.getPos()
  if (pos !== undefined) {
    props.editor.commands.setNodeSelection(pos)
  }
}

const handleImageLoad = () => {
  // 图片加载完成后的处理
}

const handleImageError = () => {
  // 图片加载错误的处理
}

const removeImage = () => {
  const pos = props.getPos()
  if (pos !== undefined) {
    props.editor.commands.deleteRange({ from: pos, to: pos + 1 })
  }
}

const handleInteractOutside = () => {
  // 点击外部时关闭 popover，但不取消图片选中状态
  showPopover.value = false
}
</script>

<style scoped>
.image-node-view {
  margin: 1em 0;
}

.image-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-container.selected {
  outline: 2px solid #5b48f1;
  outline-offset: 2px;
  border-radius: 8px;
}

.image-container:hover {
  transform: scale(1.02);
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: white;
}

.upload-progress {
  width: 80%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #5b48f1;
  transition: width 0.3s ease;
}

.upload-text {
  font-size: 12px;
  font-weight: 500;
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 38, 38, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  color: white;
  padding: 16px;
  text-align: center;
}

.error-message {
  font-size: 12px;
  margin-bottom: 8px;
}

.retry-button {
  background: white;
  color: #dc2626;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  font-weight: 500;
}

.retry-button:hover {
  background: #f3f4f6;
}
</style>

<style>
.image-settings-popover {
  padding: 12px !important;
  width: auto !important;
  min-width: 240px;
}
</style>
