import { Node, mergeAttributes } from '@tiptap/core'

export const Video = Node.create({
  name: 'video',

  group: 'block',

  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      width: {
        default: '100%',
      },
      height: {
        default: 'auto',
      },
      controls: {
        default: true,
      },
      autoplay: {
        default: false,
      },
      loop: {
        default: false,
      },
      muted: {
        default: false,
      },
      frameborder: {
        default: '0',
      },
      allowfullscreen: {
        default: true,
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'video',
      },
      {
        tag: 'iframe',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // Check if it's an iframe (for YouTube, Vimeo, etc.)
    if (HTMLAttributes.src && (HTMLAttributes.src.includes('youtube.com') || HTMLAttributes.src.includes('youtu.be') || HTMLAttributes.src.includes('vimeo.com'))) {
      return [
        'iframe', 
        mergeAttributes(HTMLAttributes, {
          width: '560',
          height: '315',
          controls: undefined,
          autoplay: undefined,
          loop: undefined,
          muted: undefined,
          frameborder: '0',
          allowfullscreen: true,
          allow: 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
        })
      ]
    }
    
    // Regular video element
    return ['video', mergeAttributes(HTMLAttributes, { controls: true }), 0]
  },

  addCommands() {
    return {
      setVideo: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})