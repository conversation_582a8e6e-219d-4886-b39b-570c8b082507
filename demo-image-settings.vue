<template>
  <div class="demo-container">
    <h1>Simple Editor - 图片设置功能演示</h1>
    
    <div class="instructions">
      <h3>功能说明：</h3>
      <ul>
        <li><strong>图片上传：</strong>点击工具栏中的图片按钮，可以从设备上传或通过URL插入图片</li>
        <li><strong>图片设置：</strong>选中图片后，工具栏会显示设置按钮（齿轮图标）</li>
        <li><strong>对齐方式：</strong>支持左对齐、居中、右对齐三种方式</li>
        <li><strong>大小调整：</strong>使用滑块调整图片大小（10%-100%），或使用快速大小按钮</li>
      </ul>
    </div>

    <div class="editor-container">
      <SimpleEditor />
    </div>

    <div class="feature-list">
      <h3>新增功能：</h3>
      <div class="feature-item">
        <h4>🎯 图片对齐方式</h4>
        <p>支持左对齐、居中、右对齐三种对齐方式，可以通过设置面板快速切换</p>
      </div>
      <div class="feature-item">
        <h4>📏 图片大小调整</h4>
        <p>使用滑块组件实现百分比大小调整（10%-100%），支持实时预览</p>
      </div>
      <div class="feature-item">
        <h4>⚡ 快速大小预设</h4>
        <p>提供25%、50%、75%、100%四个快速大小按钮，方便快速调整</p>
      </div>
      <div class="feature-item">
        <h4>🎨 用户友好界面</h4>
        <p>设置面板采用模态框设计，界面简洁直观，操作便捷</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import SimpleEditor from './registry/components/simple-editor/index.vue'
</script>

<style scoped>
.demo-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.5em;
  font-weight: 600;
}

.instructions {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.instructions ul {
  margin-bottom: 0;
  line-height: 1.6;
}

.instructions li {
  margin-bottom: 8px;
}

.instructions strong {
  color: #ffd700;
}

.editor-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.feature-list h3 {
  grid-column: 1 / -1;
  color: #2c3e50;
  font-size: 1.5em;
  margin-bottom: 20px;
  text-align: center;
}

.feature-item {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #5b48f1;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-item h4 {
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1em;
}

.feature-item p {
  color: #666;
  line-height: 1.5;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .demo-container {
    padding: 15px;
  }
  
  h1 {
    font-size: 2em;
  }
  
  .instructions {
    padding: 20px;
  }
  
  .editor-container {
    padding: 15px;
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
}
</style>
