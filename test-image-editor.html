<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Editor with Image Settings Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .instructions {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
        }
        .instructions ul {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <h1>Simple Editor - Image Settings Test</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ul>
            <li>点击工具栏中的图片按钮上传或插入图片</li>
            <li>选中图片后，工具栏会显示设置按钮（齿轮图标）</li>
            <li>点击设置按钮打开图片设置面板</li>
            <li>在设置面板中可以：
                <ul>
                    <li>调整图片对齐方式（左对齐、居中、右对齐）</li>
                    <li>使用滑块调整图片大小（10%-100%）</li>
                    <li>使用快速大小按钮（25%, 50%, 75%, 100%）</li>
                </ul>
            </li>
        </ul>
    </div>

    <div class="test-container">
        <div id="app"></div>
    </div>

    <script type="module">
        import { createApp } from 'vue'
        import SimpleEditor from './registry/components/simple-editor/index.vue'

        const app = createApp({
            components: {
                SimpleEditor
            },
            template: '<SimpleEditor />'
        })

        app.mount('#app')
    </script>
</body>
</html>
