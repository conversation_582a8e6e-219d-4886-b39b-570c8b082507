# Simple Editor - 图片设置功能

## 功能概述

为 Simple Editor 添加了图片对齐方式设置和百分比大小调整功能，提供更丰富的图片编辑体验。

## 新增功能

### 1. 图片对齐方式设置
- **左对齐**：图片靠左显示
- **居中对齐**：图片居中显示（默认）
- **右对齐**：图片靠右显示

### 2. 图片大小调整
- **滑块控制**：使用滑块组件实现 10%-100% 的百分比大小调整
- **实时预览**：调整过程中可以实时看到效果
- **快速预设**：提供 25%、50%、75%、100% 四个快速大小按钮

### 3. 用户界面
- **设置按钮**：选中图片时工具栏显示齿轮图标的设置按钮
- **模态框设计**：设置面板采用模态框形式，界面简洁直观
- **响应式布局**：支持移动端和桌面端的良好显示

## 技术实现

### 新增组件

1. **Slider 组件** (`registry/components/ui/slider/`)
   - 自定义滑块组件
   - 支持 Vue 3 Composition API
   - 可配置最小值、最大值、步长

2. **ImageSettings 组件** (`registry/components/simple-editor/components/ImageSettings.vue`)
   - 图片设置面板
   - 对齐方式选择
   - 大小调整控制

### 修改的文件

1. **image-upload.js** - 图片上传扩展
   - 添加 `align` 属性支持
   - 修改 `renderHTML` 方法支持对齐样式
   - 更新默认属性

2. **index.vue** - 主编辑器组件
   - 添加图片设置按钮
   - 集成 ImageSettings 组件
   - 添加图片对齐样式

3. **ImageUploadButton.vue** - 图片上传按钮
   - 为新插入的图片设置默认属性

## 使用方法

1. **插入图片**
   - 点击工具栏中的图片按钮
   - 选择"从设备上传"或"从URL插入"

2. **调整图片设置**
   - 点击选中图片
   - 点击工具栏中的设置按钮（齿轮图标）
   - 在弹出的设置面板中调整对齐方式和大小

3. **快速操作**
   - 使用对齐按钮快速切换对齐方式
   - 使用预设大小按钮快速调整到常用尺寸
   - 使用滑块精确调整大小

## 样式说明

### CSS 类名
- `.image-paragraph.align-left` - 左对齐图片容器
- `.image-paragraph.align-center` - 居中对齐图片容器
- `.image-paragraph.align-right` - 右对齐图片容器

### 属性说明
- `width`: 图片宽度，支持百分比（如 "50%"）
- `align`: 图片对齐方式（"left", "center", "right"）

## 兼容性

- 支持现有的图片上传功能
- 向后兼容已有的图片内容
- 支持拖拽上传和粘贴上传

## 演示

可以使用 `demo-image-settings.vue` 文件查看完整的功能演示。

## 文件结构

```
registry/components/
├── simple-editor/
│   ├── index.vue                    # 主编辑器（已修改）
│   ├── components/
│   │   ├── ImageUploadButton.vue    # 图片上传按钮（已修改）
│   │   └── ImageSettings.vue        # 图片设置面板（新增）
│   └── extensions/
│       └── image-upload.js          # 图片上传扩展（已修改）
└── ui/
    └── slider/                      # 滑块组件（新增）
        ├── index.ts
        └── Slider.vue
```
